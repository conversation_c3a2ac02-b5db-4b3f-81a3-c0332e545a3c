<template>
	<PageLayout iconColor="#000" :isBackgroundColor="false" :isShowBack="false" :navTitle="navTitle"
		:bgMaskStyle="bgMaskStyleComputed">
		<!-- 页面内容 -->
		<view class="page-order-content">
			<view class="page-order-tags">
				<view class="page-order-tags-item">
					<view class="page-order-tags-item-icon">
						<image class="page-order-tags-item-icon-img"
							src="/static/images/8d229a503a8d959340f2005e837d5ac83c1fa9d5.svg" />
					</view>
					<view class="page-order-tags-item-text">债券</view>
				</view>
				<view class="page-order-tags-item">
					<view class="page-order-tags-item-icon">
						<image class="page-order-tags-item-icon-img" src="/static/images/discount.svg" />
					</view>
					<view class="page-order-tags-item-text">优惠</view>
				</view>
				<text class="page-order-tags-suffix">
					管理
				</text>
			</view>
			<!-- 商品列表 -->
			<view class="page-order-goods-list">
				<view class="goods-container">
					<checkbox-group @change="checkboxChange">
						<label class="goods-item">
							<checkbox value="item1" :checked="true" />
							<text>商品1 - 已选中</text>
						</label>
						<label class="goods-item">
							<checkbox value="item2" :checked="false" />
							<text>商品2 - 未选中</text>
						</label>
						<label class="goods-item">
							<checkbox value="item3" :checked="false" />
							<text>商品3 - 未选中</text>
						</label>
					</checkbox-group>
				</view>
			</view>
		</view>

	</PageLayout>
</template>

<script>
import PageLayout from "@/components/PageLayout/index.vue";

export default {
	components: {
		PageLayout
	},
	data() {
		return {
			navTitle: "购物车",
			selectedItems: ['item1'] // 默认选中第一个商品
		}
	},
	methods: {
		checkboxChange(e) {
			this.selectedItems = e.detail.value;
			console.log('选中的商品:', this.selectedItems);
		}
	},
	computed: {
		bgMaskStyleComputed() {
			return {
				"--bg-mask-background": `linear-gradient(180deg, #f7d7d6 2.04%, #F5F6FA 10.69%);`
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.page-order-content {
	width: 100%;
	height: 100%;
	background-color: #f5f6fa;
	padding: 32rpx 16rpx;
}

.page-order-tags {
	width: 100%;
	height: 60rpx;
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.page-order-tags-item {
	width: fit-content;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 8rpx 16rpx;
	background-color: #EBEDF0;
	border-radius: 8px;
	gap: 8rpx;

	&-icon {
		width: 32rpx;
		height: 32rpx;

		&-img {
			width: 100%;
			height: 100%;
		}
	}

	&-text {
		color: #666;
		font-size: 24rpx;
		font-weight: 400;
	}
}

.page-order-tags-suffix {
	color: #666;
	font-size: 28rpx;
	font-weight: 400;
	margin-left: auto;
}

.page-order-goods-list {
	margin-top: 24rpx;

	.goods-container {
		background-color: #fff;
		border-radius: 16rpx;
		padding: 32rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	}

	.goods-item {
		display: flex;
		align-items: center;
		padding: 16rpx 0;
		border-bottom: 1rpx solid #f0f0f0;

		&:last-child {
			border-bottom: none;
		}

		text {
			font-size: 28rpx;
			color: #333;
			margin-left: 16rpx;
		}
	}

	// Checkbox 样式修复
	// /deep/ checkbox {
	// 	width: 40rpx !important;
	// 	height: 40rpx !important;
	// 	margin-right: 0;
	// }

	// /deep/ checkbox .uni-checkbox-input {
	// 	width: 40rpx !important;
	// 	height: 40rpx !important;
	// 	border-radius: 50%;
	// 	border: 2rpx solid #ddd;
	// 	background-color: #fff;
	// 	transition: all 0.3s ease;
	// }

	// /deep/ checkbox .uni-checkbox-input.uni-checkbox-input-checked {
	// 	border: 2rpx solid var(--view-theme, #E93323) !important;
	// 	background-color: var(--view-theme, #E93323) !important;
	// 	color: #fff !important;
	// 	transform: scale(1.1);
	// }

	// /deep/ checkbox .uni-checkbox-input.uni-checkbox-input-checked::before {
	// 	font-size: 28rpx;
	// 	color: #fff;
	// }

	// // 微信小程序兼容
	// /deep/ checkbox .wx-checkbox-input {
	// 	width: 40rpx !important;
	// 	height: 40rpx !important;
	// 	border-radius: 50%;
	// 	border: 2rpx solid #ddd;
	// 	background-color: #fff;
	// 	margin-right: 0 !important;
	// 	transition: all 0.3s ease;
	// }

	// /deep/ checkbox .wx-checkbox-input.wx-checkbox-input-checked {
	// 	border: 2rpx solid var(--view-theme, #E93323) !important;
	// 	background-color: var(--view-theme, #E93323) !important;
	// 	color: #fff !important;
	// 	transform: scale(1.1);
	// }

	// /deep/ checkbox .wx-checkbox-input.wx-checkbox-input-checked::before {
	// 	font-size: 28rpx;
	// 	color: #fff;
	// }
}
</style>